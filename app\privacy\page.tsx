'use client';

import { motion } from 'framer-motion';
import { BokehBackground } from '@/components/ui/aceternity/bokeh-background';
import { StarsBackground } from '@/components/ui/stars-background';
import { ShootingStars } from '@/components/ui/shooting-stars';
import { useLanguage } from '@/lib/context/language-context';

export default function PrivacyPage() {
  const { language } = useLanguage();

  return (
    <BokehBackground
      className="min-h-screen"
      density={25}
      speed={1.5}
      colors={['#9333ea', '#7c3aed', '#6366f1', '#8b5cf6']}
    >
      <StarsBackground />
      <ShootingStars />
      
      <div className="container mx-auto px-4 py-16 max-w-4xl relative z-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            {language === 'mn' ? 'Нууцлалын бодлого' : 'Privacy Policy'}
          </h1>
          <p className="text-gray-300 text-lg">
            {language === 'mn' 
              ? 'Таны хувийн мэдээллийг хэрхэн хамгаалж байгаа талаар' 
              : 'How we protect and handle your personal information'}
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-black/40 backdrop-blur-md rounded-xl border border-purple-500/20 p-8"
        >
          <div className="prose prose-invert max-w-none">
            <h2 className="text-2xl font-semibold text-white mb-4">
              {language === 'mn' ? 'Мэдээлэл цуглуулах' : 'Information Collection'}
            </h2>
            <p className="text-gray-300 mb-6">
              {language === 'mn'
                ? 'Бид таны хувийн мэдээллийг зөвхөн шаардлагатай тохиолдолд цуглуулж, хамгаалалттай хадгалдаг.'
                : 'We collect your personal information only when necessary and store it securely.'}
            </p>

            <h2 className="text-2xl font-semibold text-white mb-4">
              {language === 'mn' ? 'Мэдээлэл ашиглах' : 'Information Usage'}
            </h2>
            <p className="text-gray-300 mb-6">
              {language === 'mn'
                ? 'Таны мэдээллийг үйлчилгээ сайжруулах, холбоо барих зорилгоор ашигладаг.'
                : 'Your information is used to improve our services and communicate with you.'}
            </p>

            <h2 className="text-2xl font-semibold text-white mb-4">
              {language === 'mn' ? 'Мэдээлэл хамгаалах' : 'Data Protection'}
            </h2>
            <p className="text-gray-300 mb-6">
              {language === 'mn'
                ? 'Бид таны мэдээллийг хамгаалахын тулд орчин үеийн аюулгүй байдлын арга хэмжээ авч хэрэгжүүлдэг.'
                : 'We implement modern security measures to protect your information.'}
            </p>

            <div className="mt-8 p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
              <p className="text-sm text-gray-400">
                {language === 'mn'
                  ? 'Энэхүү бодлого нь 2025 оны 1-р сараас эхлэн хүчин төгөлдөр болно.'
                  : 'This policy is effective as of January 2025.'}
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </BokehBackground>
  );
}
