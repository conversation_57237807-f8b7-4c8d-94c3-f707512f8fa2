'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { BokehBackground } from '@/components/ui/aceternity/bokeh-background';
import { useLanguage } from '@/lib/context/language-context';
import TextHoverEffectDemo from '@/components/ui/text-hover-effect-demo';

export default function ProgramsPage() {
  const { t } = useLanguage();

  return (
    <>
      {/* Background Text Effect - Footer Area Only */}
      <div className="fixed bottom-0 left-0 right-0 h-[600px] z-[-10] flex items-center justify-center pointer-events-none">
        <div className="pointer-events-none">
          <TextHoverEffectDemo />
        </div>
      </div>

      {/* Hero Section with Plants/Growth Imagery */}
      <section className="relative h-[80vh] overflow-hidden bg-black">
        {/* Background Image with Plants/Growth */}
        <div className="absolute inset-0">
          <div
            className="absolute inset-0 bg-cover bg-center opacity-80"
            style={{
              backgroundImage: 'url(/images/programs/plant-bulbs.jpg)',
              backgroundPosition: 'center center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black" />
        </div>

        {/* Content */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center text-center px-4">
          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 max-w-4xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {t('programs.hero.title')}
          </motion.h1>

          <motion.div
            className="w-24 h-1 bg-primary mb-8"
            initial={{ width: 0 }}
            animate={{ width: 96 }}
            transition={{ duration: 1, delay: 0.5 }}
          />

          <motion.p
            className="text-xl text-white/80 max-w-2xl mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            {t('programs.hero.subtitle')}
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <Link href="/programs/portfolio">
              <motion.button
                className="px-8 py-3 bg-primary text-white rounded-full text-lg font-medium hover:bg-primary/90 transition-colors duration-300 shadow-lg shadow-primary/20"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
              >
                View Portfolio Companies
              </motion.button>
            </Link>
            <motion.button
              className="px-8 py-3 border border-white/20 text-white rounded-full text-lg font-medium hover:bg-white/10 transition-colors duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                document.getElementById('programs-section')?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              Explore Programs
            </motion.button>
          </motion.div>
        </div>
      </section>

      {/* Programs Section with Bokeh Background */}
      <div id="programs-section" className="relative">
        <div className="absolute inset-0 z-0">
          <div
            className="absolute inset-0 bg-cover bg-center opacity-50"
            style={{
              backgroundImage: 'url(/images/programs/1.jpg)',
              backgroundPosition: 'center center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black via-black/90 to-black" />
        </div>

        {/* Bokeh effect overlay */}
        <BokehBackground
          className="absolute inset-0 z-[1] pointer-events-none"
          colors={['#8b5cf6', '#a855f7', '#c084fc', '#e879f9', '#fbbf24', '#f59e0b']}
          density={60}
          speed={1.5}
        >
          <div className="hidden">Bokeh effect</div>
        </BokehBackground>

        <div className="relative z-10 py-24">
          {/* First Program - ХУРДАСГУУР ХӨТӨЛБӨР */}
          <div className="min-h-[100vh] flex items-center">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div className="order-2 md:order-1">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                    className="max-w-xl"
                  >
                    <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                      {t('programs.accelerator.title')}
                    </h2>
                    <div className="w-20 h-1 bg-primary mb-6"></div>
                    <p className="text-white/80 text-lg mb-8">
                      {t('programs.accelerator.description')}
                    </p>
                    <Link href="/programs/accelerator">
                      <motion.button
                        className="px-6 py-2.5 bg-primary/90 text-white rounded-full text-sm font-medium hover:bg-primary transition-colors duration-300 shadow-lg shadow-primary/20"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {t('programs.details.button')}
                      </motion.button>
                    </Link>
                  </motion.div>
                </div>
                <div className="order-1 md:order-2">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                    className="relative h-[300px] md:h-[400px] overflow-hidden rounded-xl"
                  >
                    <div
                      className="absolute inset-0 bg-cover bg-center"
                      style={{
                        backgroundImage: 'url(/images/programs/plant-bulbs.jpg)',
                        backgroundPosition: 'center center'
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  </motion.div>
                </div>
              </div>
            </div>
          </div>

          {/* Second Program - МОНГОЛ ОЮУН САНАА */}
          <div className="min-h-[100vh] flex items-center">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div className="order-1">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                    className="relative h-[300px] md:h-[400px] overflow-hidden rounded-xl"
                  >
                    <div
                      className="absolute inset-0 bg-cover bg-center"
                      style={{
                        backgroundImage: 'url(/images/programs/1.jpg)',
                        backgroundPosition: 'center center'
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  </motion.div>
                </div>
                <div className="order-2">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                    className="max-w-xl"
                  >
                    <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                      {t('programs.mongolianIdea.title')}
                    </h2>
                    <div className="w-20 h-1 bg-primary mb-6"></div>
                    <p className="text-white/80 text-lg mb-8">
                      {t('programs.mongolianIdea.description')}
                    </p>
                    <Link href="/programs/mongolian-idea">
                      <motion.button
                        className="px-6 py-2.5 bg-primary/90 text-white rounded-full text-sm font-medium hover:bg-primary transition-colors duration-300 shadow-lg shadow-primary/20"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {t('programs.details.button')}
                      </motion.button>
                    </Link>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>

          {/* Third Program - ИРЭЭДҮЙ ХӨТӨЛБӨР */}
          <div className="min-h-[100vh] flex items-center">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div className="order-2 md:order-1">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                    className="max-w-xl"
                  >
                    <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                      {t('programs.future.title')}
                    </h2>
                    <div className="w-20 h-1 bg-primary mb-6"></div>
                    <p className="text-white/80 text-lg mb-8">
                      {t('programs.future.description')}
                    </p>
                    <Link href="/programs/future">
                      <motion.button
                        className="px-6 py-2.5 bg-primary/90 text-white rounded-full text-sm font-medium hover:bg-primary transition-colors duration-300 shadow-lg shadow-primary/20"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {t('programs.details.button')}
                      </motion.button>
                    </Link>
                  </motion.div>
                </div>
                <div className="order-1 md:order-2">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                    className="relative h-[300px] md:h-[400px] overflow-hidden rounded-xl"
                  >
                    <div
                      className="absolute inset-0 bg-cover bg-center"
                      style={{
                        backgroundImage: 'url(/images/programs/2.jpg)',
                        backgroundPosition: 'center center'
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  </motion.div>
                </div>
              </div>
            </div>
          </div>

          {/* Fourth Program - INNOHUB PODCAST */}
          <div className="min-h-[100vh] flex items-center">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div className="order-1">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                    className="relative h-[300px] md:h-[400px] overflow-hidden rounded-xl"
                  >
                    <div
                      className="absolute inset-0 bg-cover bg-center"
                      style={{
                        backgroundImage: 'url(/images/programs/plant-bulbs.jpg)',
                        backgroundPosition: 'center center'
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  </motion.div>
                </div>
                <div className="order-2">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                    className="max-w-xl"
                  >
                    <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                      {t('programs.podcast.title')}
                    </h2>
                    <div className="w-20 h-1 bg-primary mb-6"></div>
                    <p className="text-white/80 text-lg mb-8">
                      {t('programs.podcast.description')}
                    </p>
                    <Link href="/programs/podcast">
                      <motion.button
                        className="px-6 py-2.5 bg-primary/90 text-white rounded-full text-sm font-medium hover:bg-primary transition-colors duration-300 shadow-lg shadow-primary/20"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {t('programs.details.button')}
                      </motion.button>
                    </Link>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </>
  );
}

