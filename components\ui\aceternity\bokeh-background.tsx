'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface BokehProps {
  children: React.ReactNode;
  className?: string;
  density?: number;
  speed?: number;
  colors?: string[];
}

export const BokehBackground = ({
  children,
  className,
  density = 35, // Optimized default density
  speed = 2.5, // Faster for snappier movement
  colors = ['#ffcc33', '#ffaa00', '#ff8800', '#ffffff'],
}: BokehProps) => {
  const [bokehElements, setBokehElements] = useState<React.ReactNode[]>([]);

  useEffect(() => {
    const elements = [];

    // Reduced to 2 layers for better performance
    for (let layer = 0; layer < 2; layer++) {
      const layerDensity = Math.floor(density / 2);

      // Simplified blur classes
      const layerBlur = layer === 0 ? 'blur-sm' : 'blur-md';

      // Simplified opacity multiplier
      const layerOpacityMultiplier = layer === 0 ? 1 : 0.6;

      for (let i = 0; i < layerDensity; i++) {
        const size = Math.random() * 80 + 40; // 40-120px reduced range
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        const delay = Math.random() * 4; // Shorter delay range
        const duration = (Math.random() * 8 + 12) / speed; // Shorter duration for snappier movement
        const color = colors[Math.floor(Math.random() * colors.length)];
        const baseOpacity = (Math.random() * 0.3 + 0.15) * layerOpacityMultiplier; // 0.15-0.45 adjusted by layer

        // Simplified movement patterns
        const movementRange = 40 + Math.random() * 20; // 40-60px movement range

        elements.push(
          <motion.div
            key={`${layer}-${i}`}
            className={`absolute rounded-full ${layerBlur}`}
            style={{
              width: `${size}px`,
              height: `${size}px`,
              left: `${x}%`,
              top: `${y}%`,
              backgroundColor: color,
              opacity: baseOpacity,
            }}
            animate={{
              x: [-movementRange/2, movementRange/2, -movementRange/2],
              y: [-movementRange/3, movementRange/3, -movementRange/3],
              opacity: [baseOpacity, baseOpacity * 1.5, baseOpacity],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration,
              repeat: Infinity,
              repeatType: 'loop',
              delay,
              ease: "easeInOut",
            }}
          />
        );
      }
    }

    // Reduced large floating elements for better performance
    for (let i = 0; i < Math.floor(density / 15); i++) {
      const size = Math.random() * 120 + 80; // 80-200px reduced size range
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      const delay = Math.random() * 6;
      const duration = (Math.random() * 15 + 20) / speed; // Faster movement
      const color = colors[Math.floor(Math.random() * colors.length)];
      const opacity = Math.random() * 0.12 + 0.08; // Slightly more visible

      elements.push(
        <motion.div
          key={`large-${i}`}
          className="absolute rounded-full blur-2xl"
          style={{
            width: `${size}px`,
            height: `${size}px`,
            left: `${x}%`,
            top: `${y}%`,
            backgroundColor: color,
            opacity,
          }}
          animate={{
            x: [-20, 20, -20],
            y: [-15, 15, -15],
            opacity: [opacity, opacity * 1.8, opacity],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration,
            repeat: Infinity,
            repeatType: 'loop',
            delay,
            ease: "easeInOut",
          }}
        />
      );
    }

    setBokehElements(elements);
  }, [density, speed, colors]);

  return (
    <div className={cn('relative overflow-hidden', className)}>
      <div className="absolute inset-0 z-0">
        {bokehElements}
        <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-10" />
      </div>
      <div className="relative z-20">{children}</div>
    </div>
  );
};

export const GlassCard = ({
  children,
  className,
  hoverEffect = true,
}: {
  children: React.ReactNode;
  className?: string;
  hoverEffect?: boolean;
}) => {
  return (
    <motion.div
      className={cn(
        'relative backdrop-blur-md bg-white/5 border border-white/10 rounded-xl overflow-hidden',
        hoverEffect && 'transition-all duration-200 hover:bg-white/8 hover:shadow-md',
        className
      )}
      initial={{ opacity: 0, y: 15 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      viewport={{ once: true }}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary/8 to-transparent opacity-40" />
      <div className="relative z-10">{children}</div>
    </motion.div>
  );
};
