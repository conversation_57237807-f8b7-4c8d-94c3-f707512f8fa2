'use client';

import { motion } from 'framer-motion';
import { BokehBackground } from '@/components/ui/aceternity/bokeh-background';
import { StarsBackground } from '@/components/ui/stars-background';
import { ShootingStars } from '@/components/ui/shooting-stars';
import { useLanguage } from '@/lib/context/language-context';

export default function TermsPage() {
  const { language } = useLanguage();

  return (
    <BokehBackground
      className="min-h-screen"
      density={25}
      speed={1.5}
      colors={['#9333ea', '#7c3aed', '#6366f1', '#8b5cf6']}
    >
      <StarsBackground />
      <ShootingStars />
      
      <div className="container mx-auto px-4 py-16 max-w-4xl relative z-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            {language === 'mn' ? 'Үйлчилгээний нөхцөл' : 'Terms of Service'}
          </h1>
          <p className="text-gray-300 text-lg">
            {language === 'mn' 
              ? 'Манай үйлчилгээ ашиглахтай холбоотой нөхцөл, дүрэм' 
              : 'Terms and conditions for using our services'}
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-black/40 backdrop-blur-md rounded-xl border border-purple-500/20 p-8"
        >
          <div className="prose prose-invert max-w-none">
            <h2 className="text-2xl font-semibold text-white mb-4">
              {language === 'mn' ? 'Үйлчилгээний зөвшөөрөл' : 'Service Agreement'}
            </h2>
            <p className="text-gray-300 mb-6">
              {language === 'mn'
                ? 'Энэхүү үйлчилгээг ашиглахдаа та дараах нөхцөлүүдийг хүлээн зөвшөөрч байна.'
                : 'By using this service, you agree to the following terms and conditions.'}
            </p>

            <h2 className="text-2xl font-semibold text-white mb-4">
              {language === 'mn' ? 'Хэрэглэгчийн үүрэг хариуцлага' : 'User Responsibilities'}
            </h2>
            <p className="text-gray-300 mb-6">
              {language === 'mn'
                ? 'Хэрэглэгч үйлчилгээг зохистой, хууль ёсны зорилгоор ашиглах үүрэгтэй.'
                : 'Users are responsible for using the service appropriately and legally.'}
            </p>

            <h2 className="text-2xl font-semibold text-white mb-4">
              {language === 'mn' ? 'Үйлчилгээний хязгаарлалт' : 'Service Limitations'}
            </h2>
            <p className="text-gray-300 mb-6">
              {language === 'mn'
                ? 'Үйлчилгээ нь "байгаа байдлаараа" үзүүлэгдэх бөгөөд ямар нэгэн баталгаа өгөхгүй.'
                : 'The service is provided "as is" without any warranties or guarantees.'}
            </p>

            <h2 className="text-2xl font-semibold text-white mb-4">
              {language === 'mn' ? 'Өөрчлөлт' : 'Modifications'}
            </h2>
            <p className="text-gray-300 mb-6">
              {language === 'mn'
                ? 'Бид энэхүү нөхцөлийг цаг хугацааны явцад өөрчлөх эрхтэй.'
                : 'We reserve the right to modify these terms at any time.'}
            </p>

            <div className="mt-8 p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
              <p className="text-sm text-gray-400">
                {language === 'mn'
                  ? 'Энэхүү нөхцөл нь 2025 оны 1-р сараас эхлэн хүчин төгөлдөр болно.'
                  : 'These terms are effective as of January 2025.'}
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </BokehBackground>
  );
}
