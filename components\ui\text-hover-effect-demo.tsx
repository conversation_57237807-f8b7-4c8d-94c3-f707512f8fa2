import React from "react";
import { TextHoverEffect } from "@/components/ui/text-hover-effect";

export default function TextHoverEffectDemo() {
  return (
    <div className="h-[40rem] flex items-center justify-center relative">
      {/* Interactive area - only the text area responds to hover */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="h-full w-full flex items-center justify-center">
          <div className="pointer-events-auto w-[600px] h-[200px] flex items-center justify-center">
            <TextHoverEffect text="Inno Hub" />
          </div>
        </div>
      </div>
    </div>
  );
}
